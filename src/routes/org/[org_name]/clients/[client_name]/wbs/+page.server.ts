import { error, fail } from '@sveltejs/kit';
import type { PageServerLoad, Actions } from './$types';
import { wbsLibraryItemSchema, wbsLibraryItemWithIdSchema } from '$lib/schemas/wbs';
import { message, superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { buildWbsItemTree } from '$lib/wbs_utils';
import { requireUser } from '$lib/server/auth';

export const load: PageServerLoad = async ({ locals, params, cookies }) => {
	// Check authentication
	await requireUser();

	const { supabase } = locals;
	const { client_name } = params;

	// Fetch the client
	const { data: client, error: clientError } = await supabase
		.from('client')
		.select('*')
		.eq('name', client_name)
		.limit(1)
		.maybeSingle();

	if (clientError || !client) {
		console.error('Error fetching client:', clientError);
		throw error(404, { message: 'Client not found' });
	}

	// Is user a client admin?
	const { data: isAdmin, error: isAdminError } = await supabase.rpc('is_client_admin', {
		client_id_param: client.client_id,
	});

	if (isAdminError) {
		console.error('Error checking client admin status:', isAdminError);
		throw error(500, { message: 'Error checking permissions' });
	}

	// Fetch all WBS libraries
	const { data: wbsLibraries, error: wbsLibrariesError } = await supabase
		.from('wbs_library')
		.select('*')
		.order('name');

	if (wbsLibrariesError) {
		console.error('Error fetching WBS libraries:', wbsLibrariesError);
		throw error(500, { message: 'Error loading WBS libraries' });
	}

	// Fetch mainWbsLibraryItems
	const { data: mainWbsLibraryItems, error: mainWbsLibraryItemsError } = await supabase
		.from('wbs_library_item')
		.select('*')
		.eq('item_type', 'Standard')
		.is('project_id', null)
		.order('code');

	if (mainWbsLibraryItemsError) {
		console.error('Error fetching main WBS library items:', mainWbsLibraryItemsError);
		throw error(500, { message: 'Error loading main WBS library items' });
	}

	// Fetch client-specific WBS items
	const { data: clientItems, error: clientItemsError } = await supabase
		.from('wbs_library_item')
		.select('*')
		.eq('client_id', client.client_id)
		.eq('item_type', 'Custom')
		.is('project_id', null)
		.order('code');

	if (clientItemsError) {
		console.error('Error fetching custom WBS items:', clientItemsError);
		throw error(500, { message: 'Error loading custom WBS items' });
	}

	// Convert flat items structure to hierarchical tree
	const mainItemsTree = buildWbsItemTree(mainWbsLibraryItems);
	const clientItemsTree = buildWbsItemTree(clientItems);

	// Create form with schema validation
	const form = await superValidate({ item_type: 'Custom' as const }, zod(wbsLibraryItemSchema));

	return {
		client,
		wbsLibraries,
		clientItems,
		clientItemsTree,
		mainWbsLibraryItems,
		mainItemsTree,
		isAdmin,
		form,
	};
};

export const actions: Actions = {
	createItem: async ({ request, locals, params }) => {
		const { supabase } = locals;
		const { client_name } = params;

		// Validate form data
		const form = await superValidate(request, zod(wbsLibraryItemSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Fetch the client
		const { data: client, error: clientError } = await supabase
			.from('client')
			.select('*')
			.eq('name', client_name)
			.limit(1)
			.maybeSingle();

		if (clientError || !client) {
			console.error('Error fetching client:', clientError);
			return fail(404, { form, error: 'Client not found' });
		}

		// Ensure client_id matches the current client
		if (form.data.client_id !== client.client_id) {
			return fail(400, { form, error: 'Invalid client ID' });
		}

		// Insert the new WBS item
		const { error: insertError } = await supabase.from('wbs_library_item').insert({
			wbs_library_id: form.data.wbs_library_id,
			code: form.data.code,
			in_level_code: form.data.in_level_code,
			description: form.data.description,
			scope: form.data.cost_scope,
			parent_item_id: form.data.parent_item_id,
			level: form.data.level,
			item_type: 'Custom',
			client_id: form.data.client_id,
			project_id: null, // Client-wide item
		});

		if (insertError) {
			console.error('Error creating WBS item:', insertError);
			return fail(500, { form, error: 'Failed to create WBS item' });
		}

		return message(form, { type: 'success', text: 'WBS item created successfully' });
	},

	deleteItem: async ({ request, locals }) => {
		const { supabase } = locals;
		// Validate form data
		const form = await superValidate(request, zod(wbsLibraryItemWithIdSchema));
		if (!form.valid) {
			return fail(400, { form });
		}

		const id = form.data.wbs_library_item_id;
		if (!id) {
			return fail(400, { error: 'Item ID is required' });
		}

		// Delete the WBS item
		const { error: deleteError } = await supabase
			.from('wbs_library_item')
			.delete()
			.eq('wbs_library_item_id', id)
			.eq('item_type', 'Custom');

		if (deleteError) {
			console.error('Error deleting WBS item:', deleteError);
			return fail(500, { error: 'Failed to delete WBS item' });
		}

		return { success: true };
	},
};
