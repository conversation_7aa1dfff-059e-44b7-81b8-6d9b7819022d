import { fail, error } from '@sveltejs/kit';
import type { Actions, PageServerLoad } from './$types';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import {
	currencies,
	editVendorSchema,
	paymentTermsOptions,
	vendorTypes,
	processVendorCreationHierarchy,
	type VendorCreationHierarchyItem,
} from '$lib/schemas/vendor';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser } from '$lib/server/auth';

export const load: PageServerLoad = async ({ locals, cookies, params }) => {
	const { user } = await requireUser();
	const { supabase } = locals;
	const { vendor_id } = params;

	// Get the vendor data
	const { data: vendor, error: vendorError } = await supabase
		.from('vendor')
		.select('*')
		.eq('vendor_id', vendor_id)
		.single();

	if (vendorError || !vendor) {
		throw error(404, 'Vendor not found');
	}

	// Check if user has access to edit this vendor
	// This will be enforced by RLS, but we can also check explicitly
	const entityId = vendor.org_id || vendor.client_id || vendor.project_id;
	if (!entityId) {
		throw error(400, 'Vendor must be associated with an organization, client, or project');
	}

	const { data: hasAccess } = await supabase.rpc('get_accessible_vendors', {
		user_id_param: user.id,
		entity_type_param: vendor.org_id ? 'organization' : vendor.client_id ? 'client' : 'project',
		entity_id_param: entityId,
	});

	if (!hasAccess || !hasAccess.find((v) => v.vendor_id === vendor_id)) {
		throw error(403, 'You do not have permission to edit this vendor');
	}

	// Get vendor creation hierarchy using the optimized RPC function
	const { data: hierarchyData, error: hierarchyError } = await supabase.rpc(
		'get_vendor_creation_hierarchy',
	);

	if (hierarchyError) {
		console.error('Error fetching vendor creation hierarchy:', hierarchyError);
		throw new Error('Failed to fetch vendor creation hierarchy');
	}

	// Process the hierarchy data into UI-friendly format
	const { organizations, clients, projects } = processVendorCreationHierarchy(
		(hierarchyData as VendorCreationHierarchyItem[]) || [],
	);

	// Prepare form data
	const formData = {
		vendor_id: vendor.vendor_id,
		name: vendor.name,
		description: vendor.description,
		org_id: vendor.org_id,
		client_id: vendor.client_id,
		project_id: vendor.project_id,
		contact_name: vendor.contact_name,
		contact_email: vendor.contact_email,
		contact_phone: vendor.contact_phone,
		contact_address: vendor.contact_address,
		website: vendor.website,
		vendor_type: vendor.vendor_type as (typeof vendorTypes)[number],
		tax_id: vendor.tax_id,
		payment_terms: vendor.payment_terms as (typeof paymentTermsOptions)[number],
		payment_terms_days: vendor.payment_terms_days,
		credit_limit: vendor.credit_limit,
		currency: (vendor.currency as (typeof currencies)[number]) || 'SEK',
		is_active: vendor.is_active ?? undefined,
		certification_info: vendor.certification_info as Record<string, string> | null,
		insurance_info: vendor.insurance_info as Record<string, string> | null,
		additional_data: vendor.additional_data as Record<string, string> | null,
	};

	const form = await superValidate(formData, zod(editVendorSchema));

	return {
		form,
		vendor,
		organizations,
		clients,
		projects,
	};
};

export const actions: Actions = {
	default: async ({ request, locals, cookies, params }) => {
		await requireUser();
		const { supabase } = locals;
		const { vendor_id } = params;

		const form = await superValidate(request, zod(editVendorSchema));

		if (!form.valid) {
			return fail(400, { form });
		}

		// Remove vendor_id from the update data
		const { vendor_id: _, ...updateData } = form.data;

		// Update the vendor
		const { data: vendor, error: vendorError } = await supabase
			.from('vendor')
			.update(updateData)
			.eq('vendor_id', vendor_id)
			.select('vendor_id, name')
			.single();

		if (vendorError) {
			console.error('Error updating vendor:', vendorError);
			return fail(500, {
				form,
				message: { type: 'error', text: 'Failed to update vendor' },
			});
		}

		throw redirect(
			`/vendor/${vendor_id}`,
			{
				type: 'success',
				message: `Vendor "${vendor.name}" updated successfully`,
			},
			cookies,
		);
	},
};
