import { fail, type RequestEvent } from '@sveltejs/kit';
import { superValidate } from 'sveltekit-superforms/server';
import { zod4 as zod } from 'sveltekit-superforms/adapters';
import { vendorSchema } from '$lib/schemas/vendor';
import { redirect } from 'sveltekit-flash-message/server';
import { requireUser } from '$lib/server/auth';

export const createVendor = async ({ request, locals, cookies }: RequestEvent) => {
	const { user } = await requireUser();
	const { supabase } = locals;

	const form = await superValidate(request, zod(vendorSchema));

	if (!form.valid) {
		return fail(400, { form });
	}

	// Prepare the vendor data
	const vendorData = {
		...form.data,
		created_by_user_id: user.id,
	};

	// Insert the vendor
	const { data: vendor, error: vendorError } = await supabase
		.from('vendor')
		.insert(vendorData)
		.select('vendor_id, name')
		.single();

	if (vendorError) {
		console.error('Error creating vendor:', vendorError);
		return fail(500, {
			form,
			message: { type: 'error', text: 'Failed to create vendor' },
		});
	}

	throw redirect(
		'/vendor',
		{
			type: 'success',
			message: `Vendor "${vendor.name}" created successfully`,
		},
		cookies,
	);
};
requireUser();
